#include "motor.h"
#include "pid.h"
#include "led.h"
#include "Serial.h"
#include "delay.h"
#include "tcs34725.h"

extern tPid pidMotor1Speed;
extern tPid pidMotor2Speed;
extern tPid pidLocationSpeed;
extern float Speed1;
extern float Speed2;

int turntime=0;
int turnsign=1;
int walktime=0;
int walksign=1;
float turnspeed = 0;


float walkspeed = 0;
float Speedleft;
float Speedright;
extern int hd[9];



extern int modenow;

extern int lastTime;


///////////////////////////////
extern int num,Flag,cross,stop;
extern uint16_t line,arm;


void MotorB_Set(int Motor3,int Motor4)//开环速度控制左/右，带限幅自行调整，速度-1000（向后）到1000（向前）可调
{
	//根据正负设置方向
	if(Motor3<0) 
	{	
		CIN1 = 0;
	  CIN2 = 1; 
	}
	else if(Motor3==0)
	{	
		CIN1 = 1;
	  CIN2 = 1; 
	}
	else 
	{	
	 CIN1 = 1;
	 CIN2 = 0;
	}
	if(Motor4<0) 
	{
		DIN1 = 1;
		DIN2 = 0;
	}
	else if(Motor4==0)
	{	
		DIN1 = 1;
	  DIN2 = 1; 
	}
	else 
	{
		DIN1 = 0;
		DIN2 = 1;
	}
	

//然后设置占空比
	if(Motor3<0)
	{
	  if(Motor3<-950) Motor3=-950;
		TIM_SetCompare3(TIM1,-Motor3);
	}
	else
	{
	  if(Motor3>950) Motor3=950;
		TIM_SetCompare3(TIM1,Motor3);
	}
	
	if(Motor4<0)
	{
	  if(Motor4<-950) Motor4=-950;
		TIM_SetCompare4(TIM1,-Motor4);
	}
	else
	{
	  if(Motor4>950) Motor4=950;
		TIM_SetCompare4(TIM1,Motor4);
	}


}

void motorPidSetSpeed(float Motor1SetSpeed,float Motor2SetSpeed)
{

	//设置Pid目标转速
	pidMotor1Speed.target_val=Motor1SetSpeed;
	pidMotor2Speed.target_val=Motor2SetSpeed;
	//PID计算控制电机
	MotorB_Set(PID_realize(&pidMotor1Speed,Speed1),PID_realize(&pidMotor2Speed,Speed2));
	
}

void carturn (int jiaodu)//原地转向函数，需要使用turnsign激活
{
	if (turnsign == 1)
	{
		if (jiaodu > 0)
		{
			turnspeed =5;
		}
		if (jiaodu < 0) 
		{
			jiaodu = -jiaodu;
			turnspeed =-5;
		}
		turntime = jiaodu;
		turnsign = 0 ;
	}
	if(turntime !=0)
	{
			if (turntime >0)
			{
				turntime --;
				motorPidSetSpeed(-turnspeed,turnspeed);
			}
			if (turntime ==0)
			{		
				motorPidSetSpeed(0,0);
			}
	}
	
}

void carwalk (int juli )//固定向前或向后行驶一段距离函数，需要使用walksign激活
{
	if (walksign == 1)
	{
		if (juli > 0)
		{
			walkspeed =5;
		}
		if (juli < 0) 
		{
			juli = -juli;
			walkspeed =-5;
		}
    walktime=juli;
		walksign = 0 ;
	}
	if(walktime !=0)
	{
		if (walktime >0)
		{
			walktime --;
			motorPidSetSpeed(walkspeed,walkspeed);
		}
		if (walktime ==0)
		{		
			motorPidSetSpeed(0,0);
		}
	}
}

//控制小车巡线函数
void Car_Run(float Left,float Right)
{
	//设置Pid目标转速
	pidMotor1Speed.target_val=Left;
	pidMotor2Speed.target_val=Right;

	pidLocationSpeed.target_val = arm;
	int run = PID_realize(&pidLocationSpeed,line);
	/*********************/
	if(Left == 0 && Right == 0)	MotorB_Set(0,0);
	else	MotorB_Set(PID_realize(&pidMotor1Speed,Speed1) - run,PID_realize(&pidMotor2Speed,Speed2) + run);
}



