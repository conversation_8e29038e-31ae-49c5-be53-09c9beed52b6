#include "sys.h"
#include "stm32f4xx.h"
#include "delay.h"
#include "led.h"
#include "key.h"
#include "timer.h"
#include "oled.h"
#include "pwm.h"
#include "control.h"
#include "encoder.h"
#include "motor.h"
#include "Serial.h"
#include "pid.h"

//#include "mpu6050.h"
//#include "inv_mpu.h"
//#include "inv_mpu_dmp_motion_driver.h" 

extern void TIM1_PWM_Init(u32 arr,u32 psc);
extern void TIM9_PWM_Init(void);
extern void KEY_Init(void);
extern u8 KEY_Scan(u8 mode);

///////////////////////////////////////////////////////
///////////////////////////////////////////////////////
extern tPid pidMotor1Speed;      
extern tPid pidMotor2Speed;      
extern tPid pidTurnSpeed;        
extern float Speed1;
extern float Speed2;
float pitch,roll,yaw; 
char OledString[100];
int key=0;




extern int cross,stop,cross_flag,num,Flag;
int w = 0;
extern int turn_flag;


int main(void)
{ 
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
	delay_init(168);             
	LED_Init();				           
	KEY_Init();                  
	TIM1_PWM_Init(1000-1,840-1);     
	TIM9_PWM_Init();
	Control_Init();                 
	Encoder_Init_TIM4();
	Encoder_Init_TIM2();            
	Serial_Init();                
	OLED_Init();                     
	PID_init();                     

//	MPU_Init();			
//  while(mpu_dmp_init())
//	{                       
//		delay_ms(200);
//		OLED_ShowString(1,1,"mpu6050 error");
//	}
	
    TIM3_Int_Init(100-1,8400-1);	   



	/*oled静态显示*/
	OLED_ShowString(1, 1, "State:");

	while(1)
	{
		key=KEY_Scan(1);		
		hw_oled();
		
		sprintf(OledString, "Vl:%5f",Speed1); 
		OLED_ShowString(3,1,OledString);

		sprintf(OledString, "Vr:%5f",Speed2); 
		OLED_ShowString(4,1,OledString);
		
//		OLED_ShowNum(1,13,num_deal(num),3);
//		OLED_ShowNum(2,1,cross,3);
//		OLED_ShowNum(2,5,cross_flag,3);
//		OLED_ShowNum(2,9,turn_flag,2);
//		OLED_ShowNum(2,13,w,2);
		OLED_ShowNum(2,1,cross_add(),3);
		cross_add();
//		if(num_deal(num) == 1 && Flag == 1 && w == 0)  
//		{
//			cross_flag = 0;
//			w = 1;
//		}
//		if(num_deal(num) == 2 && Flag == 1 && w == 0)  
//		{
//			cross_flag = 0;
//			w = 2;
//		}
//		if(num_deal(num) == 3 && Flag == 1 && w == 0)  
//		{
//			cross_flag = 0;
//			w = 3;
//		}
//		if(num_deal(num) == 4 && Flag == 1 && w == 0)  
//		{
//			cross_flag = 0;
//			w = 4;
//		}
//		if(num_deal(num) == 5 && Flag == 1 && w == 0)  
//		{
//			cross_flag = 0;
//			w = 5;
//		}
//		if(num_deal(num) == 6 && Flag == 1 && w == 0)  
//		{
//			cross_flag = 0;
//			w = 6;
//		}
//		if(num_deal(num) == 7 && Flag == 1 && w == 0)  
//		{
//			cross_flag = 0;
//			w = 7;
//		}
//		if(num_deal(num) == 8 && Flag == 1 && w == 0)  
//		{
//			cross_flag = 0;
//			w = 8;
//		}
		
		

	}
}
